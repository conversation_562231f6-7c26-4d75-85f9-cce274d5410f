# Example Environment Configuration
# Copy this file and rename to .env.development, .env.sandbox, or .env.production

# OpenSearch Configuration
OS_HOST=https://your-opensearch-endpoint.region.es.amazonaws.com
MONGO_DB_URL=mongodb+srv://admin:<EMAIL>/your-database?retryWrites=true&w=majority
REGION=us-east-1

# AWS Configuration
AWS_TRACKERS_STATIC_DATA_LAMBDA=trackers-static-name
AWS_SQS_URL=https://sqs.region.amazonaws.com/account-id/queue-name.fifo
