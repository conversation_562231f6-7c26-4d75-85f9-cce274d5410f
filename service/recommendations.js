const { getOSClient } = require("../utils/connection");
const { config } = require("../environment/index");
const { log: logger } = require("../utils/logger");
const indexName = config.INDEX.recommendations;

async function insertDoc(document) {
  const client = getOSClient();
  const response = await client.index({
    index: indexName,
    body: document,
  });
  return response.body?._id;
}

async function upsertUserRecommendations(userId, doc, type, category) {
  const client = getOSClient();
  const response = await client.search({
    index: indexName,
    body: {
      query: {
        bool: {
          must: [
            { match: { "userId.keyword": userId } }, 
            { match: { type } },
            { match: { category } },
          ],
        },
      },
    },
  });
  const id = response.body?.hits?.hits[0]?._id || null;
  if (!id) {
    return await insertDoc({ ...doc, userId });
  }
  delete doc?.createdAt;
  doc.updatedAt = new Date().toISOString();
  const updateResponse = await client.update({
    index: indexName,
    id,
    body: {
      doc: {
        ...doc,
      },
    },
  });
  logger.debug({ message: "Updated user recommendations", userId, body: doc });
  return updateResponse.body._id;
}

module.exports = {
  upsertUserRecommendations,
}
